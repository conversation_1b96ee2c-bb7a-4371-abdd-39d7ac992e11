use rong::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::sync::Arc;

use crate::lxapp::{LxApp, LxAppNavigator};

pub(crate) fn navigator_to_lxapp(ctx: JSContext, app: LxAppNavigator) -> JSResult<()> {
    let lxapp = ctx.get_user_data::<Arc<LxApp>>().unwrap();
    lxapp
        .navigator_to_lxapp(app)
        .map_err(|e| RongJSError::Error(format!("Failed to open lxapp: {}", e)))?;
    Ok(())
}
