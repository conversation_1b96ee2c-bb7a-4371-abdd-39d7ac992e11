
use serde::{Deserialize, Serialize};

/// Represents the icon displayed in a toast.
/// Mirrors the Swift ToastIcon enum.
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub enum ToastIcon {
    #[default]
    Success,
    Error,
    Loading,
    None,
}

/// Represents the position of the toast on the screen.
/// Mirrors the Swift ToastPosition enum.
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub enum ToastPosition {
    Top,
    #[default]
    Center,
    Bottom,
}

/// Configuration options for showing a toast.
/// This structure is designed to be compatible with the Swift `showToast` method.
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct ToastOptions {
    pub title: String,
    #[serde(default)]
    pub icon: ToastIcon,
    pub image: Option<String>,
    /// Duration in milliseconds.
    #[serde(default = "default_duration")]
    pub duration_ms: u32,
    #[serde(default)]
    pub mask: bool,
    #[serde(default)]
    pub position: ToastPosition,
}

fn default_duration() -> u32 {
    1500
}

/// Defines the capability of showing toast notifications.
/// This is the Service Provider Interface (SPI) that platform-specific
/// code (like in lingxia-lib) will implement.
pub trait ToastProvider: Send + Sync {
    /// Shows a toast with the given options.
    fn show_toast(&self, options: &ToastOptions);

    /// Hides the currently displayed toast.
    fn hide_toast(&self);
}
