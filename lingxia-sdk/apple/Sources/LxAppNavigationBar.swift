import SwiftUI
import Foundation
import os.log

#if os(macOS)
import AppKit
#elseif os(iOS)
import UIKit
#endif

// MARK: - Logging
private let navBarLog = OSLog(subsystem: "LingXia", category: "NavigationBar")

// MARK: - Custom Button Style
struct NavigationButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.7 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

extension Notification.Name {
    static let navigationBarStateChanged = Notification.Name("NavigationBarStateChanged")
}

/// Extension to add helper methods to swift-bridge generated NavigationBarState
extension NavigationBarState {
    static let DEFAULT_HEIGHT: CGFloat = 44
}

/// Simplified protocol for navigation bar implementations
@MainActor
public protocol NavigationBarProtocol: AnyObject {
    func updateWithConfig(_ config: NavigationBarState?)
    func hide()
    func getCalculatedContentHeight() -> CGFloat
}

/// Unified SwiftUI Navigation Bar
public struct LxAppNavigationBarView: View {
    let config: NavigationBarState?
    let onBackTapped: () -> Void
    let onHomeTapped: () -> Void
    let actualStatusBarHeight: CGFloat
    @State private var isLoading: Bool = false

    public init(
        config: NavigationBarState?,
        actualStatusBarHeight: CGFloat = 44,
        onBackTapped: @escaping () -> Void = {},
        onHomeTapped: @escaping () -> Void = {}
    ) {
        self.config = config
        self.actualStatusBarHeight = actualStatusBarHeight
        self.onBackTapped = onBackTapped
        self.onHomeTapped = onHomeTapped
    }

    public var body: some View {
        if let config = config, config.show_navbar {
            navigationBarContent(config: config)
                .background(getBackgroundColor(config: config))
                .contentShape(Rectangle()) // Ensure the entire navbar area captures touches
                .onTapGesture {
                    // Absorb tap events in navbar area to prevent system interactions
                }
        } else {
            // Hidden navbar - return empty view
            EmptyView()
        }
    }

    private func navigationBarContent(config: NavigationBarState) -> some View {
        VStack(spacing: 0) {
            // Status bar spacer (iOS only) - use same background color to avoid black border
            #if os(iOS)
            Rectangle()
                .fill(getBackgroundColor(config: config))
                .frame(height: actualStatusBarHeight)
                .contentShape(Rectangle()) // Prevent touch events from passing through
                .onTapGesture {
                    // Absorb tap events to prevent system bar interaction
                }
                .onAppear {
                    print("🔧 [NavBar SwiftUI] Status bar spacer height: \(actualStatusBarHeight)")
                }
            #endif

            // Navigation bar content - simplified layout to fix width issues
            HStack(alignment: .center, spacing: 0) {
                // Leading content (back/home buttons)
                leadingContent(config: config)
                    .frame(width: 52, height: 44, alignment: .leading)

                // Center content (title) - use Spacer for proper centering
                Spacer()
                centerContent(config: config)
                    .frame(height: 44, alignment: .center)
                Spacer()

                // Trailing content (space for capsule button)
                trailingContent(config: config)
                    .frame(width: 52, height: 44, alignment: .trailing)
            }
            .frame(height: LxAppTheme.Metrics.navigationBarHeight, alignment: .center)
            .onAppear {
                print("🔧 [NavBar SwiftUI] HStack navbar content height: \(LxAppTheme.Metrics.navigationBarHeight)")
            }
        }
        .onAppear {
            print("🔧 [NavBar SwiftUI] VStack total expected height: \(actualStatusBarHeight + LxAppTheme.Metrics.navigationBarHeight)")
        }
    }

    @ViewBuilder
    private func leadingContent(config: NavigationBarState) -> some View {
        // Priority logic: show back button first, only show home button if no back button
        if config.show_back_button {
            Button(action: {
                print("NavigationBar: Back button tapped")
                onBackTapped()
            }) {
                LxAppIcons.back
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(getTextColor(config: config))
                    .frame(width: 44, height: 44)
                    .contentShape(Rectangle())
            }
            .buttonStyle(NavigationButtonStyle())
        } else if config.show_home_button {
            // Only show home button if back button is not shown
            Button(action: {
                print("NavigationBar: Home button tapped")
                onHomeTapped()
            }) {
                Image(systemName: "house")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(getTextColor(config: config))
                    .frame(width: 44, height: 44)
                    .contentShape(Rectangle())
            }
            .buttonStyle(NavigationButtonStyle())
        } else {
            Color.clear.frame(width: 44, height: 44)
        }
    }

    @ViewBuilder
    private func centerContent(config: NavigationBarState) -> some View {
        if isLoading {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(0.8)
        } else {
            Text(config.title_text.toString())
                .font(LxAppTheme.Typography.navigationTitle)
                .foregroundColor(getTextColor(config: config))
                .lineLimit(1)
                .frame(height: 44, alignment: .center)
        }
    }

    @ViewBuilder
    private func trailingContent(config: NavigationBarState) -> some View {
        // Reserve space for capsule button to ensure proper alignment
        #if os(iOS)
        Color.clear.frame(
            width: LxAppTheme.Metrics.capsuleButtonWidth + LxAppTheme.Metrics.capsuleTrailingMargin,
            height: 44
        )
        #else
        // macOS handles capsule buttons differently
        Color.clear.frame(width: 44, height: 44)
        #endif
    }

    private func getBackgroundColor(config: NavigationBarState) -> Color {
        let platformColor = PlatformColor(argb: config.background_color)
        return Color(platformColor)
    }

    private func getTextColor(config: NavigationBarState) -> Color {
        let textStyle = config.text_style.toString()
        return textStyle == "white" ? Color.white : Color.black
    }
}

/// SwiftUI ViewModifier for adding navigation bar to any view
public struct LxAppNavigationBarModifier: ViewModifier {
    let config: NavigationBarState?

    public init(config: NavigationBarState?) {
        self.config = config
    }

    public func body(content: Content) -> some View {
        VStack(spacing: 0) {
            if let config = config, config.show_navbar {
                LxAppNavigationBarView(config: config)
            }
            content
        }
    }
}

#if os(iOS)
import UIKit

/// UIKit wrapper for SwiftUI LxAppNavigationBarView on iOS
@MainActor
public class iOSNavigationBarWrapper: UIView, NavigationBarProtocol {
    private var hostingController: UIHostingController<LxAppNavigationBarView>?
    private var currentConfig: NavigationBarState?
    private var appId: String?
    private var currentPath: String?

    public override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = UIColor.clear
        setupNotificationObserver()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        backgroundColor = UIColor.clear
        setupNotificationObserver()
    }

    private func setupNotificationObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNavigationBarStateChanged),
            name: .navigationBarStateChanged,
            object: nil
        )
    }

    @objc private func handleNavigationBarStateChanged(_ notification: Notification) {
        os_log("📱 [NavBar] handleNavigationBarStateChanged notification received", log: navBarLog, type: .info)

        guard let userInfo = notification.userInfo,
              let notificationAppId = userInfo["appId"] as? String,
              let notificationPath = userInfo["path"] as? String,
              let appId = self.appId,
              let currentPath = self.currentPath,
              notificationAppId == appId && notificationPath == currentPath else {
            os_log("📱 [NavBar] Notification ignored - appId or path mismatch", log: navBarLog, type: .info)
            return
        }

        os_log("📱 [NavBar] Processing notification for appId: %{public}@, path: %{public}@",
               log: navBarLog, type: .info, appId, currentPath)

        // Refresh navbar config from Rust
        let newConfig = LxPageNavigation.getNavigationBarState(appId: appId, path: currentPath)
        updateWithConfig(newConfig)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    public func updateWithConfig(_ config: NavigationBarState?) {
        os_log("📱 [NavBar] updateWithConfig called", log: navBarLog, type: .info)

        if let config = config {
            os_log("📱 [NavBar] Config: show_navbar=%{public}@, show_back_button=%{public}@, show_home_button=%{public}@, title='%{public}@'",
                   log: navBarLog, type: .info,
                   config.show_navbar ? "true" : "false",
                   config.show_back_button ? "true" : "false",
                   config.show_home_button ? "true" : "false",
                   config.title_text.toString())
            os_log("📱 [NavBar] Background color: 0x%{public}08X, text_style: %{public}@",
                   log: navBarLog, type: .info,
                   config.background_color,
                   config.text_style.toString())
        } else {
            os_log("📱 [NavBar] Config is nil - hiding navbar", log: navBarLog, type: .info)
        }

        self.currentConfig = config

        guard let config = config else {
            hide()
            return
        }

        if config.show_navbar {
            os_log("📱 [NavBar] Showing navbar and updating SwiftUI view", log: navBarLog, type: .info)
            updateSwiftUINavigationBar()
            isHidden = false

            // Debug: Print navbar frame and title position
            DispatchQueue.main.async {
                let navbarFrame = self.frame
                os_log("📱 [NavBar] DEBUG - Navbar frame: (%.1f, %.1f, %.1f, %.1f)", log: navBarLog, type: .info, navbarFrame.origin.x, navbarFrame.origin.y, navbarFrame.size.width, navbarFrame.size.height)
                os_log("📱 [NavBar] DEBUG - Navbar height: %.1f", log: navBarLog, type: .info, navbarFrame.height)
            }
        } else {
            os_log("📱 [NavBar] Hiding navbar (show_navbar=false)", log: navBarLog, type: .info)
            hide()
        }
    }

    public func setContext(appId: String, path: String) {
        self.appId = appId
        self.currentPath = path
    }

    public func hide() {
        isHidden = true
        hostingController?.view.isHidden = true
    }

    public func getCalculatedContentHeight() -> CGFloat {
        guard let config = currentConfig, config.show_navbar else {
            return 0
        }
        
        // Use consistent status bar height calculation
        let actualStatusBarHeight = window?.windowScene?.statusBarManager?.statusBarFrame.height ?? 44
        return NavigationBarState.DEFAULT_HEIGHT + actualStatusBarHeight
    }

    private func updateSwiftUINavigationBar() {
        guard let config = currentConfig else { return }

        // Get actual status bar height for consistency
        let actualStatusBarHeight = window?.windowScene?.statusBarManager?.statusBarFrame.height ?? 44

        // If hosting controller already exists, just update the root view instead of recreating
        if let existingController = hostingController {
            let navigationBarView = LxAppNavigationBarView(
                config: config,
                actualStatusBarHeight: actualStatusBarHeight,
                onBackTapped: { [weak self] in
                    self?.handleBackButtonTap()
                },
                onHomeTapped: { [weak self] in
                    self?.handleHomeButtonTap()
                }
            )
            existingController.rootView = navigationBarView
            return
        }

        // Only create new hosting controller if none exists
        let navigationBarView = LxAppNavigationBarView(
            config: config,
            actualStatusBarHeight: actualStatusBarHeight,
            onBackTapped: { [weak self] in
                self?.handleBackButtonTap()
            },
            onHomeTapped: { [weak self] in
                self?.handleHomeButtonTap()
            }
        )

        // Create hosting controller
        let hostingController = UIHostingController(rootView: navigationBarView)
        hostingController.view.backgroundColor = UIColor.clear
        self.hostingController = hostingController

        // Add to view hierarchy
        addSubview(hostingController.view)
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    private func handleBackButtonTap() {
        // Find parent view controller and handle back navigation
        if let parentVC = findParentViewController() {
            LxAppPageNavigation.handleBackButtonClick(in: parentVC)
        }
    }

    private func handleHomeButtonTap() {
        // Find parent view controller and handle home navigation
        if let parentVC = findParentViewController() {
            // Navigate to home/initial route using LxAppPageNavigation
            if let appId = self.appId {
                let lxappInfo = getLxAppInfo(appId)
                let initialRoute = lxappInfo.initial_route.toString()
                LxAppPageNavigation.navigateToPage(
                    targetPath: initialRoute,
                    isReplace: true,
                    in: parentVC
                )
            }
        }
    }

    private func findParentViewController() -> (any LxAppViewControllerProtocol)? {
        var responder: UIResponder? = self
        while let nextResponder = responder?.next {
            if let viewController = nextResponder as? (any LxAppViewControllerProtocol) {
                return viewController
            }
            responder = nextResponder
        }
        return nil
    }
}

public typealias LingXiaNavigationBar = iOSNavigationBarWrapper
public typealias PlatformNavigationBar = iOSNavigationBarWrapper
#elseif os(macOS)
public typealias LingXiaNavigationBar = LxAppNavigationBarView
public typealias PlatformNavigationBar = LxAppNavigationBarView
#endif

public extension View {
    /// Adds navigation bar to the view using Rust-provided configuration
    func lxAppNavigationBar(config: NavigationBarState?) -> some View {
        self.modifier(LxAppNavigationBarModifier(config: config))
    }
}
