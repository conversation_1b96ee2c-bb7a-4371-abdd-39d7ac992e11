{"permissions": {"allow": ["<PERSON><PERSON>(node src/builder.js)", "Bash(node ../../../src/builder.js)", "Bash(node src/builder.js ../examples/demo/homelxapp)", "Bash(ls -la /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic.iife.js)", "Bash(rm /Users/<USER>/github/LingXia/examples/demo/homelxapp/pages/todo/logic.js)", "Bash(rm /Users/<USER>/github/LingXia/examples/demo/homelxapp/lib/utils.js)", "Bash(ls -la /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic*)", "Bash(ls -la /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic.js)", "Bash(ls -la /Users/<USER>/github/LingXia/examples/demo/homelxapp/.lingxia-build)", "Bash(mv src/builder.js src/dependency-bundler.js)", "Bash(grep -n \"Page(\" /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic.js)", "Bash(node src/dependency-bundler.js ../examples/demo/homelxapp)", "Bash(grep -n \"LingXiaLogic\" src/dependency-bundler.js)", "Bash(git checkout /Users/<USER>/github/LingXia/examples/demo/homelxapp/pages/todo/index.js)", "Bash(grep \"exports\\.\" /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic.js)", "Bash(find . -name \"*.js\" -exec grep -l \"main\\|cli\\|#!/usr/bin/env\" {} ;)", "Bash(node cli.js ../examples/demo/homelxapp)", "Bash(grep -n \"export\" vite-plugin.js)", "Bash(grep -n \"buildStart\\|writeBundle\" vite-plugin.js)", "Bash(node src/unified-builder.js ../examples/demo/homelxapp)", "Bash(grep -A5 -B5 \"Page\\|App\\|openLxApp\\|addTodo\" /Users/<USER>/github/LingXia/examples/demo/homelxapp/dist/logic.js)"], "deny": []}}