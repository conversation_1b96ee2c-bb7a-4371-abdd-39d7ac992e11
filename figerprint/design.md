# 设备指纹设计方案

## 概述

设计一个在 iOS 和 Android 平台上获取设备指纹（Device Fingerprint）的方案，且 Android 不依赖 Google 服务（如 Google Service ID），确保方案的跨平台兼容性、唯一性和稳定性，同时兼顾隐私合规性。

## 设计目标

- **唯一性**：生成能够唯一标识设备的指纹，在设备不重置（恢复出厂设置）的情况下保持稳定
- **跨平台兼容性**：方案同时适用于 iOS 和 Android，确保一致性
- **不依赖 Google 服务**：Android 设备指纹生成不依赖 Google Play 服务（如 Advertising ID）
- **隐私合规**：遵循 GDPR、CCPA 和 Apple 的隐私政策，避免收集敏感信息
- **稳定性**：设备指纹在系统更新或应用重装后保持不变
- **安全性**：防止指纹被伪造或篡改

## 方案设计

### 1. 核心思路

设备指纹通过收集设备的非敏感硬件和软件信息，结合加密算法生成唯一标识符。

#### 关键组件
- **硬件信息**：设备型号、系统信息（避免 MAC 地址等需要权限的信息）
- **软件信息**：设备名称、语言设置
- **持久化标识**：随机生成的 UUID（应用卸载后仍保持一致）
- **加密算法**：SHA-256 哈希处理，确保指纹不可逆且长度统一

#### 设计约束
- iOS 和 Android API 限制不同，需分别处理
- 避免使用需要权限的敏感信息（MAC 地址、IMEI、手机号等）
- Android 10+ 对序列号访问受限
- 必须确保应用卸载后指纹仍保持不变

### 2. 跨平台实现策略

#### 平台特定方案
- **iOS**：使用 Keychain 存储 UUID
- **Android**：使用 AndroidID

### 3. 信息收集策略

#### 设备信息收集点

| 信息类别 | 具体信息 | iOS 获取方式 | Android 获取方式 | 稳定性 |
|----------|----------|-------------|------------------|--------|
| 硬件信息 | 设备型号 | `UIDevice.model` | `Build.MODEL` | 高 |
| 硬件信息 | 制造商 | - | `Build.MANUFACTURER` | 高 |
| 硬件信息 | 设备名称 | `UIDevice.name` | `Build.DEVICE` | 中 |
| 软件信息 | 系统版本 | `UIDevice.systemVersion` | `Build.VERSION.RELEASE` | 中 |
|            |            |                          |                         |        |
|            |            |                          |                         |        |
| 软件信息 | 系统指纹 | - | `Build.FINGERPRINT` | 中 |
| 软件信息 | 语言设置 | `NSLocale` | `Locale` | 低 |
| 显示信息 | 屏幕分辨率 | `UIScreen` | `DisplayMetrics` | 低 |
| 传感器信息 | 电池信息 | `UIDevice.battery` | `BatteryManager` | 中 |
| 传感器信息 | 摄像头信息 | `AVFoundation` | `CameraManager` | 中 |
| 传感器信息 | 传感器列表 | `CoreMotion` | `SensorManager` |  |
|            |            |                          |                         |        |
| 系统信息 | CPU信息 | - | `/proc/cpuinfo` | 高 |
|            |            |                          |                         |        |
| 系统信息 | 内存信息 | - | `ActivityManager` | 中 |
| 持久化标识 | UUID | Keychain 存储 | Android Keystore 存储 | 高 |

#### Android 设备标识符多层级回退策略

**2. Media DRM ID**
- 获取方式：`MediaDrm.getPropertyByteArray(MediaDrm.PROPERTY_DEVICE_UNIQUE_ID)`
- 稳定性：🟡 工厂重置后通常保持（部分厂商例外）
- 处理：使用SHA-256哈希处理原始Widevine ID
- 优势：不依赖Google服务，广泛兼容
- 超时：600ms（CI测试环境600s）



#### 重要说明
- **不使用 MAC 地址**：需要 `ACCESS_WIFI_STATE` 权限，Android 10+ 返回随机化 MAC
- **不使用序列号**：Android 10+ 需要 `READ_PHONE_STATE` 权限且返回随机值
- **不使用 identifierForVendor**：iOS 应用卸载后会重置
- **存储机制关键**：必须使用系统级安全存储确保应用卸载后数据保留
- **异常安全**：所有API调用都包含超时和异常处理机制（1-3秒）
- **厂商例外**：部分厂商（如Vivo、Samsung）的Media DRM ID在工厂重置后可能改变

### 4. 生成流程

#### 步骤概览
1. **信息收集**：收集上述设备信息点
2. **设备标识符获取**：按照多层级回退策略获取设备ID
3. **UUID 管理**：首次运行生成 UUID，持久化存储
4. **数据拼接**：将所有信息拼接成字符串
5. **哈希处理**：使用 SHA-256 生成固定长度指纹
6. **缓存存储**：可选本地缓存，减少重复计算

#### 异常安全机制
- **超时保护**：所有API调用设置1-3秒超时，防止ANR
- **线程安全**：使用专用线程池执行耗时操作
- **异常捕获**：全局异常处理，确保应用稳定
- **降级策略**：主要方法失败时自动切换备选方案

#### 关键考虑
- **跨平台一致性**：生成算法需保证 iOS 和 Android 结果一致
- **持久化存储**：UUID 存储必须使用系统级安全存储（Keychain/Android Keystore）
- **应用卸载保护**：确保应用卸载后 UUID 仍然保留
- **权限最小化**：避免使用需要额外权限的 API
- **版本化管理**：使用显式版本号防止算法变更导致指纹意外改变

#### 版本管理策略
- **版本号范围**：V1-V6（根据实现复杂度递增）
- **向后兼容**：新版本保持向后兼容性
- **显式指定**：调用时必须指定版本号，避免使用默认值
- **版本隔离**：不同版本的指纹结果相互独立

### 5. 隐私合规框架

#### 合规要求
- **GDPR 合规**：明确告知、用户同意、数据最小化
- **CCPA 合规**：提供隐私选择、数据透明度
- **Apple 政策**：避免敏感信息、明确使用目的

#### 实际权限需求
基于 FingerprintJS 实现，实际需要的最小权限集合：

**Android 权限**
```xml
<uses-permission android:name="android.permission.USE_FINGERPRINT"/>
<uses-permission android:name="android.permission.USE_BIOMETRIC"/>
<uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
```

**权限说明**
- **USE_FINGERPRINT/USE_BIOMETRIC**：用于生物识别验证（可选）
- **READ_GSERVICES**：仅用于获取GSF ID，非必需
- **无敏感权限**：不涉及位置、通讯录、相机等隐私权限

#### 实施策略
- **信息最小化**：仅收集必要信息，避免个人身份信息
- **用户同意**：收集前获取明确同意，提供选择退出机制
- **数据安全**：加密存储、安全传输、本地处理为主
- **透明度**：提供隐私政策说明，明确告知数据用途
- **数据本地化**：指纹数据主要在设备本地生成和存储
- **匿名化处理**：所有标识符都经过哈希处理，无法逆向追踪

#### 隐私保护技术措施
- **哈希处理**：Media DRM ID使用SHA-256哈希
- **本地存储**：避免数据传输到服务器
- **超时机制**：防止长时间占用系统资源
- **异常处理**：确保异常情况下不会泄露敏感信息

### 6. 风险评估

#### 技术风险
- **系统更新**：可能改变设备信息，影响指纹稳定性
- **设备重置**：恢复出厂设置导致本地数据丢失
- **API 变更**：平台 API 更新可能影响信息收集
- **厂商差异**：不同Android厂商对Media DRM ID的处理方式不同
- **超时风险**：某些设备上API调用可能超时

#### 合规风险
- **权限要求**：某些信息可能需要运行时权限
- **地区差异**：不同地区隐私法规要求不同
- **用户拒绝**：用户可能拒绝数据收集
- **政策变化**：平台隐私政策可能收紧

#### 性能风险
- **ANR风险**：同步API调用可能导致应用无响应
- **内存占用**：大量设备信息收集可能占用内存
- **电量消耗**：频繁的传感器访问可能影响电池寿命

#### 缓解策略
- **多因素验证**：结合多种信息源提高稳定性
- **回退机制**：主要方法失败时的备选方案
- **定期评估**：持续监控法规变化和技术更新
- **超时保护**：所有API调用设置超时限制
- **异步处理**：耗时操作在后台线程执行
- **缓存机制**：避免重复计算，提高性能
- **厂商适配**：针对已知厂商问题提供特殊处理
- **版本管理**：通过版本控制应对API变更

### 7. 实施建议

#### 开发阶段
1. **可行性研究**：验证各平台 API 可用性
2. **原型开发**：实现核心算法和存储机制
3. **测试验证**：多设备、多系统版本测试
4. **性能优化**：缓存机制、算法优化

#### 测试策略
- **设备覆盖**：测试不同厂商、系统版本、设备型号
- **场景测试**：工厂重置、系统更新、应用重装等场景
- **性能测试**：内存占用、CPU使用率、响应时间
- **稳定性测试**：长时间运行、高并发调用
- **异常测试**：网络异常、权限拒绝、API超时

#### 上线阶段
1. **逐步推广**：小范围试点，逐步扩大
2. **监控机制**：建立指纹生成成功率监控
3. **用户反馈**：收集用户体验和隐私顾虑
4. **持续优化**：基于数据和反馈持续改进

#### 监控指标
- **生成成功率**：指纹生成成功的比例
- **响应时间**：从调用到获得结果的时间
- **错误率**：各种异常情况的发生频率
- **缓存命中率**：缓存机制的有效性
- **用户接受度**：用户对指纹识别的接受程度

#### 维护策略
- **版本兼容**：确保新版本与旧版本指纹的兼容性
- **问题跟踪**：建立用户反馈和问题跟踪机制
- **定期更新**：根据平台更新和法规变化调整实现
- **文档维护**：保持技术文档和用户文档的同步更新



## 总结

本方案提供了一个平衡技术可行性和隐私合规性的设备指纹设计方案。通过跨平台统一的信息收集策略、安全的数据存储机制和加密算法，能够在不依赖 Google 服务的前提下，生成稳定且唯一的设备标识符。

方案实施过程中需要重点关注隐私合规性要求，建立完善的风险监控和缓解机制，并根据实际应用场景和技术环境进行适当调整。







前台：APP 的视角，面向门店伙伴和加盟商的。

1.  每个租户有唯一的编码，租户自己配置。 配置后不可修改。
2.  用户输入编码，根据编码跳出认证模式。只接受一个方式： 钉钉、飞书、企业微信、自建（短期不做）。
3.  MVP 需要支持钉钉，企业微信。                



后台（APP 或 web）： 总部人员

1. 每个租户有唯一的编码，
2. 输入编码跳出认证方式
3. only one OA



认证跟 App 走。 一个租户只有一个后台的概念， 前台可以多个（泰柯，7 分甜， 连信）







权限组（概念，督导： 可以使用哪些软件， 每个软件的功能权限和数据权限）

* 自定义
* 系统提供一部分（联动门店主数据的）
* 只绑定人



人员变更回调处理权限变更： 规则怎么配置？ 联动钉钉人员字段，比如岗位，部门 ->  重新设置属于哪个权限组。

支持 audit

