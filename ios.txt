xcodebuild -workspace LingXiaWebViewExample.xcworkspace -scheme LingXiaWebViewExample -destination 'platform=iOS,id=00008030-001C1DC11A82402E' build CODE_SIGN_IDENTITY="" CODE_SIGNING_REQUIRED=NO

CODE_SIGN_IDENTITY="": 跳过代码签名
CODE_SIGNING_REQUIRED=NO: 不要求代码签名
Debug配置: 包含调试信息，未优化
依赖解析: 自动解析本地SPM包依赖
这样的设置允许您在没有开发者证书的情况下编译App，但要安装到真机上仍需要有效的开发者证书和配置文件。


1. 为真机编译 (需要开发者证书):
xcodebuild -workspace LingXiaWebViewExample.xcworkspace -scheme LingXiaWebViewExample -destination 'platform
=iOS,id=00008030-001C1DC11A82402E' build

2. 为模拟器编译:
xcodebuild -workspace LingXiaWebViewExample.xcworkspace -scheme LingXiaWebViewExample -destination 'platform
=iOS Simulator,name=iPhone 16,OS=18.3.1' build

3. 清理构建:
xcodebuild clean -workspace LingXiaWebViewExample.xcworkspace -scheme LingXiaWebViewExample

4. 查看可用的schemes:
xcodebuild -workspace LingXiaWebViewExample.xcworkspace -list


=== SPM包编译命令示例 ===
1. 构建包:
swift build

2. 运行测试:
swift test

3. 清理构建:
swift package clean

4. 查看包信息:
swift package describe


Tuist 本身不直接编译安装 .app 文件，但它通过：
生成 100% 标准 Xcode 项目
提供 快捷命令封装（tuist build）
支持 签名/多平台/扩展等原生能力
优化 CI/CD 构建流程

# 方式1：使用 Xcode 手动编译（图形界面）
open MyApp.xcworkspace

# 方式2：命令行编译
tuist build --scheme MyApp --device "iPhone 15"
# 等效于：xcodebuild build -scheme MyApp


# 安装到连接的 iOS 设备
tuist build --scheme MyApp --device "Your_Device_Name"

# 安装到模拟器（自动启动）
tuist build --scheme MyApp --simulator "iPhone 15"
xcrun simctl install booted ./DerivedData/Build/Products/Debug-iphonesimulator/MyApp.app


tuist signing \
  --path ./ \
  --bundle-id com.example.myapp \
  --team-id ABC123XYZ \
  --provisioning-profile "App Store"

=====
SPM
mkdir MyPackage
cd MyPackage
swift package init --type library


在 Xcode 项目中：
File > Add Packages
输入仓库 URL：https://github.com/yourname/MyPackage.git
选择版本规则

xcrun devicectl list devices




tuist generate && xcodebuild -workspace LingXiaWebViewExample.xcworkspace -scheme LingXiaWebViewExample -destination 'platform=iOS,name=iPhone (2)' build
install app
xcrun devicectl device install app --device 00008030-001C1DC11A82402E /Users/<USER>/Library/Developer/Xcode/DerivedData/LingXiaWebViewExample-geaxqwfmuhdhvscjzubvkcckpafn/Build/Products/Debug-iphoneos/LingXiaWebViewExample.app
start app
xcrun devicectl device process launch --device 00008030-001C1DC11A82402E XTL-PRF552H98X.app.lingxia.example.miniapp

cargo rustc --crate-type=staticlib 只创建静态库，不解析外部符号
cargo  rustc --crate-type=staticlib --release  --target aarch64-apple-ios


log stream --device 00008030-001C1DC11A82402E --predicate 'process == "LingXiaWebViewExample"' --style compact
