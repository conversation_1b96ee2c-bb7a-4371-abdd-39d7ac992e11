import AppKit
import lingxia
import CLingXiaFFI

class LingXiaAppDelegate: NSObject, NSApplicationDelegate {

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Enable WebView debugging BEFORE LxApp.initialize()
        // This ensures debugging is enabled before the first WebView is created
        LxApp.enableWebViewDebugging()

        // Initialize LingXia system
        LxApp.initialize()

        // Option 1: Use predefined device size (convenient)
        macOSLxApp.setWindowSize(.iPhoneSE)
        macOSLxApp.setWindowStyle(.capsuleStyle)

        // Option 2
        //macOSLxApp.setWindowStyle(.tabStyle)

        // Test navbar update with background delay
        testNavbarUpdateWithDelay()
    }

    private func testNavbarUpdateWithDelay() {
        // Test navbar update after 3 seconds
        DispatchQueue.global(qos: .background).asyncAfter(deadline: .now() + 3.0) {
            print("🧪 [Test] Calling updateNavBarUI after 3 seconds...")

            // Test updating navbar for current page
            let appid = RustString("homelxapp")
            let result = LxApp.updateNavBarUI(appid: appid.as_str())

            print("🧪 [Test] updateNavBarUI result: \(result)")
        }

        // Test another update after 6 seconds
        DispatchQueue.global(qos: .background).asyncAfter(deadline: .now() + 6.0) {
            print("🧪 [Test] Calling updateNavBarUI after 6 seconds...")

            // Test updating navbar for current page
            let appid = RustString("homelxapp")
            let result = LxApp.updateNavBarUI(appid: appid.as_str())

            print("🧪 [Test] updateNavBarUI result: \(result)")
        }

        // Test home page update after 9 seconds
        DispatchQueue.global(qos: .background).asyncAfter(deadline: .now() + 9.0) {
            print("🧪 [Test] Calling updateNavBarUI after 9 seconds...")

            // Test updating navbar for current page
            let appid = RustString("homelxapp")
            let result = LxApp.updateNavBarUI(appid: appid.as_str())

            print("🧪 [Test] updateNavBarUI result: \(result)")
        }
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// Entry point
let app = NSApplication.shared
let delegate = LingXiaAppDelegate()
app.delegate = delegate
app.run()
