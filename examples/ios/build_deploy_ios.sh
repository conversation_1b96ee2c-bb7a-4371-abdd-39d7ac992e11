#!/bin/bash

# Exit on error
set -e

# Parse command line arguments
SKIP_RUST=false
for arg in "$@"
do
    case $arg in
        --skip-rust)
        SKIP_RUST=true
        ;;
    esac
done

# Get the absolute path of the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
LINGXIA_ROOT="$SCRIPT_DIR/../.."
WORKSPACE_ROOT="$LINGXIA_ROOT" # Workspace root is the same as LingXia root

# Define the resources directory for iOS
RESOURCES_DIR="$SCRIPT_DIR/lxapp/Sources/lxapp/Resources"
echo RESOURCES_DIR: $RESOURCES_DIR

# Build Rust library for iOS unless --skip-rust flag is set
if [ "$SKIP_RUST" = false ]; then
    echo "Building Rust library for iOS with Swift bridge headers..."
    cd "$WORKSPACE_ROOT"
    cargo rustc --crate-type=staticlib --release --target aarch64-apple-ios -p lingxia-lib
else
    echo "Skipping Rust library build as requested."
fi

mkdir -p "$RESOURCES_DIR"

# Clean resources directory before copying new files
echo "Cleaning resources directory..."
rm -rf "$RESOURCES_DIR"/*

echo "Copying lingxia-view files to resources..."
cp "$LINGXIA_ROOT/lingxia-view/404.html" "$RESOURCES_DIR/"
cp "$LINGXIA_ROOT/lingxia-view/webview-bridge.js" "$RESOURCES_DIR/"

echo "Copying host app configuration..."
cp "$LINGXIA_ROOT/examples/demo/app.json" "$RESOURCES_DIR/"

echo "Building and copying demo LxApp..."
cd "$LINGXIA_ROOT/examples/demo/homelxapp"
if [ -f "package.json" ] ; then
    # Copy built LxApp to resources with proper directory structure
    if [ -d "dist" ]; then
        echo "Copying built LxApp to resources..."
        mkdir -p "$RESOURCES_DIR/homelxapp"
        cp -R dist/* "$RESOURCES_DIR/homelxapp/"
    else
        echo "Error: dist directory not found, copying source files..."
        exit 1
    fi
else
    echo "Error: package.json not found"
    exit 1
fi

echo "Building and deploying iOS app..."
cd "$SCRIPT_DIR/lxapp"

# Try xtool's automatic signing first
echo "Attempting xtool automatic deployment..."
if env LINGXIA_PROJECT_ROOT=$LINGXIA_ROOT xtool dev; then
    echo "✅ xtool automatic deployment successful!"
    exit 0
fi

echo "xtool automatic deployment failed, trying manual signing..."
env LINGXIA_PROJECT_ROOT=$LINGXIA_ROOT xtool dev  build

echo "Signing app with development certificate..."
# Use the known working certificate
CERT_ID="E2DC6570FA48C5C3806C85645382E7B36FD7B541"
echo "Using certificate: $CERT_ID"

# Sign the app with entitlements
codesign --force --sign "$CERT_ID" --entitlements App.entitlements xtool/lxapp.app

echo "Finding connected iOS device..."
# Get the first connected iOS device
DEVICE_ID=$(xcrun devicectl list devices | grep "paired" |  head -1 | awk '{print $3}')
if [ -z "$DEVICE_ID" ]; then
    echo "Error: No connected iOS device found"
    exit 1
fi
echo "Installing to device: $DEVICE_ID"

echo "Installing app to device..."
xcrun devicectl device install app --device "$DEVICE_ID" xtool/lxapp.app
