{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_harmony_-ecwNoaVzUFg5RFNoyhP_QI4Sh2TFlkeS83mwDIJiMQ=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B535106609D5DAE8389BA50920DEE715CAADAEA9AE6238B6CD1DA548D3037CF071B40F24AE8B97D",
          "profile": "/Users/<USER>/.ohos/config/default_harmony_-ecwNoaVzUFg5RFNoyhP_QI4Sh2TFlkeS83mwDIJiMQ=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_harmony_-ecwNoaVzUFg5RFNoyhP_QI4Sh2TFlkeS83mwDIJiMQ=.p12",
          "storePassword": "0000001B80D57D75892857B081B5BC0C78F9915D4AD38054BB1B516621F9F9008C398316BA7CAA22FA1DB7"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "targetSdkVersion": "5.0.5(17)",
        "compatibleSdkVersion": "5.0.5(17)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "lingxia",
      "srcPath": "./../../lingxia-sdk/harmony/lingxia",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}
