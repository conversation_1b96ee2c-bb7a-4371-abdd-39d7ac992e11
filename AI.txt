主要使用两个工具来查询代码索引：

🔍 主要工具
1. codebase-retrieval - 智能语义搜索
这是最强大的工具，使用 AI 语义理解

codebase-retrieval: "Find TabBar API methods like showTabBar, hideTabBar, setTabBarStyle"

优势：

🧠 语义理解 - 理解代码意图，不只是字符串匹配
🎯 智能检索 - 能找到相关但不完全匹配的代码
📊 跨文件关联 - 能找到分散在不同文件中的相关代码
🔗 上下文感知 - 理解代码结构和依赖关系

2. view 工具的正则搜索
用于精确的模式匹配：
view: path="file.swift", search_query_regex="func.*[Tt]ab[Bb]ar|showTabBar|hideTabBar"

优势：

⚡ 快速精确 - 直接正则匹配
🎯 模式匹配 - 支持复杂的搜索模式
📍 行号定位 - 精确显示匹配位置
🔍 上下文显示 - 显示匹配行的前后文

在这次对话中，我主要使用了以下工具来查询和修改本地文件：

📋 文件查询工具
1. view - 查看文件内容
查看完整文件或指定行范围
支持正则表达式搜索
最常用的文件查看工具
2. codebase-retrieval - 智能代码搜索
根据自然语言描述搜索相关代码
跨文件查找特定功能或结构
用于理解代码架构和依赖关系
🔧 文件修改工具
1. str-replace-editor - 精确文本替换
基于行号的精确替换
支持多个替换操作
主要的代码编辑工具
2. save-file - 创建新文件
创建全新文件
不能用于修改现有文件
🚀 构建和测试工具
1. launch-process - 执行命令
运行构建脚本
执行测试命令
查看构建结果
2. read-terminal - 读取终端输出
获取命令执行结果
查看构建日志

view - 文件查看工具
{
  "path": "lingxia-lib/src/harmony/ffi.rs",
  "type": "file",
  "view_range": [200, 280]
}

{
  "path": "lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets",
  "type": "file",
  "search_query_regex": "export.*class|export.*function"
}



str-replace-editor - 文本替换工具
{
  "command": "str_replace",                    // 必需：命令类型
  "path": "string",                           // 必需：文件路径
  "instruction_reminder": "string",           // 必需：固定提醒文本
  "old_str_1": "string",                     // 必需：要替换的原文本
  "new_str_1": "string",                     // 必需：新文本
  "old_str_start_line_number_1": number,     // 必需：原文本开始行号
  "old_str_end_line_number_1": number,       // 必需：原文本结束行号
  // 支持多个替换操作：old_str_2, new_str_2, old_str_start_line_number_2...
}


launch-process - 命令执行工具
{
  "command": "string",           // 必需：要执行的shell命令
  "wait": boolean,              // 必需：是否等待命令完成
  "max_wait_seconds": number,   // 必需：最大等待时间（秒）
  "cwd": "string"              // 必需：工作目录的绝对路径
}

save-file
{
  "instructions_reminder": "string",    // 必需：固定提醒文本
  "path": "string",                    // 必需：文件路径
  "file_content": "string",            // 必需：文件内容
  "add_last_line_newline": boolean     // 可选：是否在文件末尾添加换行符（默认true）
}

read_terminal
{
  "only_selected": boolean  // 可选：是否只读取选中的文本（默认false）
}

diagnostics 
{
  "paths": ["string"]  // 必需：要检查的文件路径数组
}


Builtin tools
bash:



lingxia-sdk/android/lingxia  lingxia-lib/src/android/ffi.rs现在让我们对android 做同样的目标重构， example app 添加测试触发 update tabbar ui， 前期 ffi 返回随机 tabar bade 和 redot 信息。examples/android/build.sh 这是构建命令，支持 skip-rust