figureprintid

webview/url crate:
1. export webtag method, for future: desktop, lx.openHttps(webtag, url)
2. new API to turn on/off dom, or add parameter(openHttps inside know it needs to turn off dom) to create webview ????????????
3. tabbar item path allow https://..., use host as webtag
4. create webview directly, without page binding (without logic service)
use cause: open deekseek etc
Think: how to allow inject scripts (like chrom extension) e.g. scraw

float:
1. one page
2. lx.openFloatWindow(path?)l, like navigatorto with query paramter
3. in html, usw lx.loadData(x after ?) to reload, loadData must be fast API
4. float_window: center/bottom, dimension: 3/4 - 1/2, close button
5. lx.closeFloatWindow()
use scenario:
1. show message/notification
2. picker/selecter: support loading resoruce from server, lxapp predownload
2. payment

remove LxAppInfo

lxapp stack in rust: openlx push stack, close pop
    rust: push stack, swift; pop from stack on closing
lxapp hold current path, by default/bootstrap, initial route page is current path, on_page_show: set current page path
click on tabitem: switch tab doen by rust

LxAppInfo -> get_app_name or get_current_page(return pageInfo)

event sysytem:
switchPage: tabbar item, home button -> switch page
backpress: page stack


pub show_back_navigation: home/back, --> if no back, show home ?  page stack is empty and it's not tab bar page, show home. it allow no navigationbar, but show back button(new style)
pubs how_capsule_button: decided at UI side
func navigate(
    appId: String,
    to path: String,
    with navigationType: WXNavigationType
)
enum WXNavigationType {
    case forward
    case backward
    case replace/redirect
    case switchTab
    case Launch  -> for openLxap to open entry page
}
when it's switchTab, UI find tabbar index by path, since UI know tabbar list
on_lxapp_opened: switch page ?


简单正向传值：优先使用 URL Query。
反向传值：强烈推荐使用 事件通道 (Event Channel)，这是最规范、最可控的方式。 rong的 event 是不是可以直接有个呢？

微信小程序的 picker 都是原生实现的，如何做数据交互呢？ view -> native:picker -> choose -> view: show to user -> native: business
lx.picker(date), lx.picker(province), lx.picker(multipel, "data") 系统相册，小程序临时文件
门店数据如何给？（格式， lxapp 自己根据业务来获取门店信息列表，然后给 picker）

Push Mesage：
badge： controlled by server completely. if message read, tell server, server then update badge
为每个主要渠道（如华为应用市场、小米应用商店、Google Play）编译一个单独的APK（渠道包）。每个APK只包含对应渠道的推送SDK（例如，华为渠道包只集成HMSCore Push）。这是目前业界最主流、最推荐的做法。
lx.cloud.eventsource(): SSE, e.g  川流， 蓝幕.  就像 pushmessage， 业务自己完成绑定关系来做推送

server API return app list and type(H5 url, or lxapp), name, icon
New API 平台： 针对业务场景定义 prompt， 支持版本或 preview， 客户可以定制或优化

like setData, adding postMessage to support post message from native js to view/webview
postmessage(message, origin) origin is optional
e.x. push LLM reply to view

borderles
tabbar: text at right side automaticaly

macOSx: tab
1. icon
2. x 悬停效果， 标签悬停效果。 微信
3. tab： API， tab 可以直接是 webview，或者 terminal

lingxia-mcp: server w/ sse. sse can run javascript to control lxapp
     SSE client -- SSE server on PC/MPC server --- MPC client
     integrate into cli
     lxapp must be in debug mode
     GUI: devtool, log view

lingxia-cli: tempalte, new, build, standalone



lingxia builer:
  better name -> publish
  support bun etc. currently, hardcode 'npm'

openlxapp: with extra flags + debug



about user agent
https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/web-default-useragent



devtools with MCP
1. capture log. realtime  with filter susbsytem/category
2. tools for view: click etc. opendia
3. capture, switch Page(tab)
4. hotreload -- upgrade lxapp packages and restart
5. how to capture request/response for logic fetch

integrate to cloud:
1. download and update
2. login(user, device info)
3. remote log trace(appid, path). collect and zip, upload to cloud
4. cloud function like wechat. lx.cloud.callFunc("name", {})

App event: on_backgorupd, on_capture

LX JS API:
体验版,开发版本 基础版本号

Rong:
  Harmony JVM(code done, but not test)
  SSE
  ReadableStream
  V8

extra(lower priority):
printer support
scanner



tauri  icon:
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-codegen/src/context.rs#L465
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-utils/src/config.rs#L1225

lx:// Get
https: Get or others. -> is allowed domain ->check lx:// https://  yes, continue to go
                      -> is should proxed -> yes. build Request -> let lxapp to proxy

                          not allowed: reject directly
                          allow: proxyed, or direct to response

                          continue to process
                          take over then proces

                          ios: does not support intercept resoruce request in https ?
                          anroid: only supprot get method ?

 adb shell run-as com.lingxia.example.lxapp cp -r /data/user/0/com.lingxia.example.lxapp/files/lingxia/lxapps/homelxapp/. /data/user/0/com.lingxia.example.lxapp/files/lingxia/lxapps/95dc2dcfcccc191/


- 应用进入后台时 (applicationDidEnterBackground)
- 内存警告时 (didReceiveMemoryWarning)
