[package]
name = "lingxia-webview"
version = "0.1.0"
edition = "2024"

[dependencies]
lxapp = { package = "lingxia-lxapp", path = "../lingxia-lxapp" }
log = "0.4.27"

# Use workspace dependencies
serde_json = { workspace = true }
http = { workspace = true }

# Android-specific dependencies
[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21.1"
android_logger = "0.15.0"

# iOS/macOS-specific dependencies for proper os_log integration
[target.'cfg(any(target_os = "ios", target_os = "macos"))'.dependencies]
oslog = "0.2.0"
# objc2 dependencies for direct WebView management
objc2 = "0.6"
objc2-web-kit = "0.3"
objc2-foundation = "0.3"
# block2 for Objective-C blocks and delegates
block2 = "0.6"
# GCD dispatch for main thread operations
dispatch2 = "0.3"

[target.'cfg(all(target_os = "linux", target_env = "ohos"))'.dependencies]
ohos_hilog = "0.1"
napi-ohos = "1.1.0"
napi-derive-ohos = "1.0.4"
ohos-raw-sys="0.1.0"
ohos-web-sys="0.0.1"
