<!DOCTYPE html>
<html>
<head>
    <title>LingXia App</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <h1>LingXia</h1>
    <p>请在app中打开以获得最佳体验</p>
    <a href="intent://www.lingxia.app/12/3#Intent;scheme=https;package=com.lingxia.example.lxapp;end">
        在app中打开
    </a>
</body>
</html>


intent:// - 固定前缀，必须的
www.lingxia.app/12/3 - 目标URL的host和path部分
#Intent; - 分隔符，必须的
scheme=https - 必须的，指定原始URL的scheme
package=com.lingxia.example.lxapp - 可选但推荐，指定目标app包名，避免弹出选择器
end - 结束标记，必须的

action=android.intent.action.VIEW - 通常不需要，默认就是VIEW
category=android.intent.category.BROWSABLE - 通常不需要
S.browser_fallback_url=https://www.lingxia.app/12/3 - 如果app未安装，跳转到这个URL

所以最核心的是：intent:// + URL + #Intent;scheme=https;end，package参数强烈推荐加上。



# 启用域名
adb shell pm set-app-links-allowed --package com.lingxia.example.lxapp --user cur true

# 设置域名状态为approved
adb shell pm set-app-links --package com.lingxia.example.lxapp --state approved www.lingxia.app

# 重新验证
adb shell pm verify-app-links --re-verify com.lingxia.example.lxapp


# check status
adb shell pm get-app-links com.lingxia.example.lxapp


❯ hdc shell aa start -b app.lingxia.lxapp.example -A "ohos.want.action.viewData" -U "https://www.lingxia.app/user/profile"



支持 iOS App Link (Universal Link) 需要在您的服务器上放置一个名为 `apple-app-site-association` 的文件。

以下是您需要的文件名、存放路径和文件内容。

---

### 文件名

`apple-app-site-association`
(注意：文件名完全就是这个，**没有 `.json` 后缀**)

---

### 存放路径

您需要将这个文件上传到您 `www.lingxia.app` 域名的服务器上。有两个存放位置可供选择，**优先推荐第一个**:

1.  `https://www.lingxia.app/.well-known/apple-app-site-association`
2.  `https://www.lingxia.app/apple-app-site-association`

---

### 文件内容

这是一个 JSON 文件。您需要将下面的 `YOUR_TEAM_ID` 替换为您自己的 Apple Developer Team ID。

```json
{
  "applinks": {
    "details": [
      {
        "appID": "YOUR_TEAM_ID.app.lingxia.example.lxapp",
        "paths": [
          "*"
        ]
      }
    ]
  }
}
```

**内容解析:**
*   `appID`: 由您的 **Team ID** 和应用的 **Bundle ID** (`app.lingxia.example.lxapp`) 组合而成。
*   `paths`: 一个数组，定义了此 App 可以处理的域名路径。`"*"` 是一个通配符，代表您希望能处理 `https://www.lingxia.app/` 下的所有路径。您也可以指定更具体的路径，例如 `"/user/*"`。

---

### **‼️ 重要注意事项**

1.  **替换 Team ID**: 您**必须**将 `YOUR_TEAM_ID` 替换为真实的 ID，否则将无法生效。您可以在 [Apple Developer 网站](https://developer.apple.com/account/) 的 "Membership" 部分找到您的 Team ID。
2.  **服务器配置**: 您的服务器在提供这个文件时，其 HTTP Header 的 `Content-Type` **必须**是 `application/json`。
3.  **HTTPS**: 该文件必须通过 HTTPS 提供，并且不能有任何重定向。



https://www.lingxia.app/.well-known/apple-app-site-association


```json
{
  "applinks": {
    "details": [
      {
        "appID": "linuxsir320.app.lingxia.example.lxapp",
        "paths": [
          "*"
        ]
      }
    ]
  }
}
```


 xcrun devicectl list devices
 ❯  xcrun devicectl device info apps --device 24DDAFE5-18AC-5FD2-BEC5-63F4789623E5
13:55:04  Acquired tunnel connection to device.
13:55:04  Enabling developer disk image services.
13:55:04  Acquired usage assertion.
Apps installed:
Name    Bundle Identifier                          Version   Bundle Version
-----   ----------------------------------------   -------   --------------
lxapp   XTL-PRF552H98X.app.lingxia.example.lxapp   1.0.0     1
七开得胜    com.sweet7.s7app                           1.9.0     1.9.0
codesign -dv examples/ios/lxapp/xtool/lxapp.app



❯ xtool ds profiles list | grep -A15 "ACTIVE" | head -20
  profile state: ACTIVE
  uuid: 2f8ae3ae-79ef-453a-aef5-e155001014a6
  created date: 2025/8/13, 18:48
  expiration date: 2025/8/20, 18:48
    team identifiers:
    - PRF552H98X
    certificates:
    - 2C41CB64830A97977B3F5D3B9B88577C
    devices:
    - 00008030-001C1DC11A82402E
    entitlements: {"get-task-allow":true,"application-identifier":"PRF552H98X.XTL-PRF552H98X.app.lingxia.example.lxapp","com.apple.developer.team-identifier":"PRF552H98X","keychain-access-groups":["PRF552H98X.*"]}

  xtool 添加了XTL-PRF552H98X


  xcrun devicectl device info apps --device 24DDAFE5-18AC-5FD2-BEC5-63F4789623E5 | grep -A2 -B2 lxapp
